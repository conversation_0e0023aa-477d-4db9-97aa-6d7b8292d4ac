import { useEffect, useState } from 'react';
import SearchFilter from 'shared/muiComponents/SearchFilter';
import { updateIfChanged } from 'utils/utilFunctions';
import { searchUsers } from 'services/userService';
import { User } from 'types/user';

interface UserFilterProps {
  filters?: { id: string; name: string }[];
  onChange: (filters: { id: string; name: string }[]) => void;
  width?: string;
}

export default function UserFilter({ filters, onChange, width }: UserFilterProps) {
  const [currentFilters, setCurrentFilters] = useState<{ id: string; name: string }[]>(
    Array.isArray(filters) ? filters : []
  );

  const handleChange = (values: string[]) => {
    // Each value should be in the format "id|name"
    const newFilters = values.map((value) => {
      const [id, name] = value.split('|');
      return { id, name: name || id };
    });
    setCurrentFilters(newFilters);
    onChange(newFilters);
  };

  const onSearch = async (query: string) => {
    const usersFound = await searchUsers(query, 'name', true, false, []);
    return usersFound.map((user: User) => {
      const displayName = user.name || `${user.firstName} ${user.lastName}` || user.email;
      return `${user.id}|${displayName}`;
    });
  };

  useEffect(() => {
    updateIfChanged(setCurrentFilters, Array.isArray(filters) ? filters : []);
  }, [filters]);

  return (
    <SearchFilter<string>
      getLabel={(f) => {
        const [id, name] = f.split('|');
        return name || id;
      }}
      label="Assigned To"
      onChange={handleChange}
      onSearch={onSearch}
      selected={currentFilters.map((f) => `${f.id}|${f.name}`)}
      variant="filter"
      width={width}
    />
  );
}
