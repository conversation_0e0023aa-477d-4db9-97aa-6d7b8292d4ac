/* eslint-disable consistent-return */
import httpStatus from 'http-status';

import { Op, QueryTypes } from 'sequelize';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { startCase, uniq } from 'lodash';
import db from '../models';

import ApiError from '../helpers/api-error';
import { sequelize } from '../config/database';
import notificationService from './notification.service';
import { AwardPaymentOuput } from '../models/types/award-payment';
import { AwardNotificationInput } from '../models/types/award-notification';
import { scheduleLogger } from '../config/logger';

import strings from '../config/strings';

const { parseHTMLTemplate, sendEmailLetter } = require('../helpers/mail.helper');

const DEFAULT_FREQUENCY = 2; // 30 // days

dayjs.extend(relativeTime);

const {
  Client,
  AwardNotification,
  Award,
  AwardPayment,
  AwardUserRole,
  NotificationPreference,
  Employee,
  ProgramFiles,
  Program,
} = db;

const getNextDueReports = async () => {
  try {
    const results = await sequelize.query(
      `SELECT
        ar.id AS "reportId",
        ar.name AS "name",
        ar.due_date as "dueDate",

        a.string_id AS "stringId",
        a.id AS "awardId",
        a.funder AS "funder",
        a.client_id AS "clientId",
        a.assignee_id AS "assigneeId",
        a.grant_program_name as "grantProgramName",
        a.award_amount as "awardAmount",

        jsonb_build_object(
          'id', c.id,
          'name', c.name
        ) AS "client"
      FROM
        "award_reports" ar
      INNER JOIN "awards" a
        ON ar."award_id" = a.id
      INNER JOIN "clients" c
        ON a.client_id = c.id
      WHERE
        ar."due_date" = (
          SELECT MIN("due_date")
          FROM "award_reports"
          WHERE "award_id" = ar."award_id"
        )
        AND
          ar."due_date" BETWEEN NOW() AND NOW() + INTERVAL '30 days'
      ORDER BY
        a.id, ar."due_date";
      `,
      { type: QueryTypes.SELECT }
    );

    // scheduleLogger.info(`getNextDueReports -> results: ${results.length}`);

    return results;
  } catch (error) {
    console.error('Error fetching next due reports:', error);
    throw new ApiError(httpStatus.BAD_REQUEST, `Error creating project`);
  }
};

const sendUpcomingReportReminders = async () => {
  try {
    const upcomingReportsDue = await getNextDueReports();

    // scheduleLogger.info(
    //   `sendUpcomingReportReminders -> upcomingReportsDue: ${
    //     upcomingReportsDue.length
    //   }, ids: ${upcomingReportsDue.map((report: any) => report.reportId)}`
    // );

    const notifyUsers = upcomingReportsDue.map(async (report: any) => {
      const client = await Client.findByPk(report.clientId);
      if (!client) return;

      const { privateAwardsManagement = false } = client.dataValues || {};

      const user = await Employee.findOne({
        where: {
          id: report.assigneeId,
        },
      });

      // Skip if private management is enabled and user is not millennium admin
      if (privateAwardsManagement && user?.userType !== strings.users.userTypes.millenniumAdmin) {
        return;
      }

      const hasBeenNotified = await AwardNotification.findOne({
        where: {
          enabled: true,
          sourceId: report.reportId,
          type: 'reportDue',
          createdAt: {
            [Op.gt]: dayjs().subtract(DEFAULT_FREQUENCY, 'D').toDate(),
          },
        },
      });

      const daysFromNow = dayjs(report.dueDate).fromNow();
      const daysSinceNow = dayjs(report.dueDate).toNow(true);
      const subject =
        parseInt(daysSinceNow, 10) >= 0
          ? `Due date for ${report.name ?? ''} has passed: ${daysSinceNow}`
          : `Due date for ${report.name ?? ''} is approaching: ${daysFromNow}`;

      if (!hasBeenNotified && user) {
        const notification: AwardNotificationInput = {
          sourceId: report.reportId,
          sourceType: 'report',
          type: 'reportDue',
          subject,
          body: 'Please make sure to upload all relevant documents to avoid fines and penalties.',
        };
        await notificationService.create(notification, [report.assigneeId]);

        // scheduleLogger.info(
        //   `sendUpcomingReportReminders -> notification: ${JSON.stringify(notification)}`
        // );

        // scheduleLogger.info(`sendUpcomingReportReminders -> user: ${JSON.stringify(user)}`);

        const sentEmail = await sendEmailLetter({
          to: user.email,
          subject: notification.subject,
          plainver: notification.body,
          htmlver: parseHTMLTemplate(`${__dirname}/../email_templates/award-report-reminder.html`, {
            username: user.name,
            awardLink: `${process.env.PUBLIC_URL}dashboard/award/${report.awardId}/details`,
            dueDate: dayjs(report.dueDate).format('MMM D, YYYY'),
            funder: report.funder,
            grantProgramName: report.grantProgramName,
            awardAmount: report.awardAmount,
            clientName: client.name || client.dataValues.name, // Add client name here
          }),
        });

        scheduleLogger.info(
          `sendUpcomingReportReminders -> sentEmail: ${JSON.stringify(sentEmail)}`
        );
      }
    });

    await Promise.all(notifyUsers);
    return true;
  } catch (error) {
    console.error(error);
    throw new ApiError(httpStatus.BAD_REQUEST, `Error creating report reminders`);
  }
};

const sendComplianceReminder = async () => {
  try {
    const awards = await Award.findAll({
      raw: true,
      where: "status = :status AND compliance_due_date BETWEEN NOW() AND NOW() + INTERVAL '1 days'",
      replacements: { status: 'active' },
    });

    // scheduleLogger.info(`sendComplianceReminder -> awards: ${awards}`);

    const notifyUsers = awards.map(async (award: any) => {
      const client = await Client.findByPk(award.clientId);
      if (!client) return;

      const { privateAwardsManagement = false, usersCreatedByClient = [] } =
        client.dataValues || {};

      // If private management is enabled, only include millennium admin users
      let userIds: number[] = [];
      if (privateAwardsManagement) {
        userIds = uniq(
          [
            ...usersCreatedByClient
              .filter((user: any) => user?.userType === strings.users.userTypes.millenniumAdmin)
              .map((user: any) => user?.id),
          ].filter(Boolean)
        );
      } else {
        userIds = uniq(
          [
            ...[...usersCreatedByClient].map((user) => user.userType?.match('admin') && user?.id),
            award.assigneeId,
          ].filter(Boolean)
        );
      }

      const hasBeenNotified = await AwardNotification.findOne({
        where: {
          sourceId: award.id,
          type: 'compliance',
          createdAt: {
            [Op.gt]: dayjs().subtract(DEFAULT_FREQUENCY, 'D').toDate(),
          },
        },
      });

      // scheduleLogger.info(`sendComplianceReminder -> hasBeenNotified: ${hasBeenNotified}`);
      // scheduleLogger.info(`sendComplianceReminder -> award: ${award}`);
      // scheduleLogger.info(`sendComplianceReminder -> userIds: ${userIds}`);
      // scheduleLogger.info(`sendComplianceReminder -> award.assigneeId: ${award.assigneeId}`);
      // scheduleLogger.info(
      //   `sendComplianceReminder -> usersCreatedByClient: ${usersCreatedByClient}`
      // );

      if (!hasBeenNotified && userIds.length > 0) {
        await Promise.all(
          award.payments?.map((payment: AwardPaymentOuput) =>
            notificationService.create(
              {
                sourceId: payment.reportId,
                sourceType: 'award',
                type: 'compliance',
                subject: `You have pending payments updates for award ${award.stringId ?? ''}`,
                body: 'Please ensure to mark it as paid or reimbursed to avoid fines or penalties.',
              },
              userIds.map((id) => id.toString())
            )
          )
        );
      }
      return true;
    });

    await Promise.all(notifyUsers);
    return true;
  } catch (error) {
    console.error(error);
    throw new ApiError(httpStatus.BAD_REQUEST, `Error creating compliance reminders`);
  }
};

const markPaymentsReminders = async () => {
  try {
    const awards = await Award.findAll({
      where: {
        status: 'active',
      },
      include: [
        {
          model: AwardPayment,
          where: {
            status: {
              [Op.notIn]: ['paid', 'reimbursed'],
            },
          },
          as: 'payments',
        },
      ],
    });

    const notifyUsers = awards.map(async (award: any) => {
      const client = await Client.findByPk(award.clientId);
      if (!client) return;

      const { privateAwardsManagement = false } = client.dataValues || {};

      // If private management is enabled, only process millennium admin users
      const userIds = uniq([award.assigneeId].filter((user) => user));
      const awardLink = `${process.env.PUBLIC_URL}dashboard/award/${award.id}/details`;

      const hasBeenNotified = await AwardNotification.findOne({
        where: {
          sourceId: award.id,
          type: 'reportReminder',
          createdAt: {
            [Op.gt]: dayjs().subtract(DEFAULT_FREQUENCY, 'D').toDate(),
          },
        },
      });

      // scheduleLogger.info(`markPaymentsReminders -> hasBeenNotified: ${hasBeenNotified}`);
      // scheduleLogger.info(`markPaymentsReminders -> award: ${award}`);
      // scheduleLogger.info(`markPaymentsReminders -> userIds: ${userIds}`);

      if (!hasBeenNotified) {
        await Promise.all(
          award.payments?.map(async (payment: AwardPaymentOuput) => {
            const user = await Employee.findByPk(payment.userId);

            // Skip if private management is enabled and user is not millennium admin
            if (
              privateAwardsManagement &&
              user?.userType !== strings.users.userTypes.millenniumAdmin
            ) {
              return;
            }

            const emailAttributes = {
              sourceId: payment.reportId,
              sourceType: 'report',
              type: 'reportReminder',
              subject: `You have pending payments updates for award ${award.stringId ?? ''}`,
              body: 'Please ensure to mark all pending payments as paid or reimbursed to avoid fines or penalties.',
            };

            if (user?.email) {
              notificationService.create(emailAttributes as AwardNotificationInput, userIds);

              const formattedAwardAmount = new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'USD',
              }).format(award.awardAmount);

              return sendEmailLetter({
                from: process.env.SERVICE_MAILER_EMAIL,
                to: user.email,
                subject: emailAttributes.subject,
                plainver: `View award details here: ${awardLink}`,
                htmlver: parseHTMLTemplate(
                  `${__dirname}/../email_templates/award-status-change.html`,
                  {
                    username: user.name,
                    awardLink,
                    ...award,
                    clientName: client.name || client.dataValues.name, // Add client name here
                    awardAmount: formattedAwardAmount,
                    status: `"${startCase(award.status)}"`,
                    description: emailAttributes.body,
                  }
                ),
              });
            }
          })
        );
      }
      return true;
    });

    await Promise.all(notifyUsers);
    return true;
  } catch (error) {
    console.error(error);
    throw new ApiError(httpStatus.BAD_REQUEST, `Error creating payment reminders`);
  }
};

async function sendEmailDigest(frequency: string) {
  scheduleLogger.info(`Starting sendEmailDigest for frequency: ${frequency}`);

  const preferences = await NotificationPreference.findAll({
    where: { frequency, preference: 'awardBatchEmails' },
    include: ['user'],
  });

  scheduleLogger.info(`sendEmailDigest -> Found ${preferences.length} user preferences for ${frequency} frequency`);

  if (preferences.length === 0) {
    scheduleLogger.info(`No users found with ${frequency} email digest preferences`);
    return;
  }

  const sendEmailPromises = preferences.map(async (userPref: any) => {
    const { user } = userPref;

    scheduleLogger.info(`sendEmailDigest -> Processing user: ${user?.email || 'unknown'} (ID: ${user?.id || 'unknown'})`);

    if (user) {
      const notifications = await AwardNotification.findAll({
        where: {
          userId: user.id,
          read: false,
        },
        include: [
          {
            model: Employee,
            attributes: ['id', 'name'],
            as: 'user',
          },
        ],
      });

      scheduleLogger.info(`sendEmailDigest -> Found ${notifications.length} notifications for user ${user?.email}`);

      if (notifications.length > 0) {
        const groupedNotifications = notifications.reduce((acc: any, notification: any) => {
          const type = startCase(notification.type);

          if (!acc[type]) {
            acc[type] = [];
          }

          acc[type].push({
            message: notification.subject,
            date: dayjs(notification.createdAt).format('MMM D, YYYY'),
          });

          return acc;
        }, {});

        scheduleLogger.info(`sendEmailDigest -> Grouped notifications for ${user?.email}:`, Object.keys(groupedNotifications));

        const htmlver = parseHTMLTemplate(`${__dirname}/../email_templates/awards-digest.html`, {
          username: user.name,
          groupedNotifications,
        });

        try {
          const result = await sendEmailLetter({
            from: process.env.SERVICE_MAILER_EMAIL,
            to: user.email,
            subject: 'Millenium Digest',
            plainver: `View award details hereHeres your ${frequency} digest for Millenium Strategies:`,
            htmlver,
          });
          scheduleLogger.info(`sendEmailDigest -> Email sent successfully to ${user?.email}`, result);
        } catch (error) {
          scheduleLogger.error(`sendEmailDigest -> Failed to send email to ${user?.email}:`, error);
        }
      } else {
        scheduleLogger.info(`sendEmailDigest -> No notifications to send for user ${user?.email}`);
      }
    }

    return true;
  });
  Promise.all(sendEmailPromises);
}

const sendPaymentReminderToProjectDirectors = async () => {
  try {
    // const pastDates = Array.from({ length: 12 }, (_, i) =>
    //   dayjs().subtract((i + 1) * 30, "days").startOf("day").toDate()
    // );
    const pastDates = [
      ...Array.from({ length: dayjs().date() - 1 }, (_, i) =>
        dayjs()
          .subtract(i + 1, 'day')
          .startOf('day')
          .toDate()
      ),
      ...Array.from({ length: 7 }, (_, i) =>
        dayjs()
          .subtract(i + 1, 'day')
          .startOf('day')
          .toDate()
      ),
    ];

    const paidPayments = await AwardPayment.findAll({
      where: {
        type: 'advance',
        paidAt: { [Op.in]: pastDates },
        enabled: true,
      },
      attributes: ['awardId', 'amount', 'paidAt'],
    });

    if (!paidPayments.length) {
      console.log('No payments matching 30-day intervals.');
      return;
    }
    const awardIds = [...new Set(paidPayments.map((p: any) => p.awardId))];
    const projectDirectors = await AwardUserRole.findAll({
      where: {
        awardId: { [Op.in]: awardIds },
        role: 'projectDirector',
        enabled: true,
      },
      attributes: ['userId', 'awardId'],
    });
    if (!projectDirectors.length) {
      console.log('No project directors found for these awards.');
      return;
    }
    const projectDirectorIds = [...new Set(projectDirectors.map((p: any) => p.userId))];
    const employees = await Employee.findAll({
      where: { id: { [Op.in]: projectDirectorIds } },
      attributes: ['id', 'name', 'email'],
    });
    const employeeMap = new Map(employees.map((e: any) => [e.id, e.dataValues]));

    const notifyProjectDirectors = projectDirectors?.map(async (director: any) => {
      const user: any = employeeMap.get(director.userId);
      if (!user || !user.email) return;

      const payment = paidPayments.find((p: any) => p.awardId === director.awardId);
      if (!payment) return;

      // Get client name for the award
      const award = await Award.findByPk(director.awardId);
      let clientName = 'Unknown';
      if (award) {
        const client = await Client.findByPk(award.clientId);
        if (client) {
          clientName = client.name || client.dataValues.name;
        }
      }

      const paidDate = dayjs(payment.paidAt).format('MMM D, YYYY');
      const subject = `Payment Reminder: ${paidDate}`;
      const awardLink = `${process.env.FRONTEND_PORT}/awards/${director.awardId}`;

      await sendEmailLetter({
        from: process.env.SERVICE_MAILER_EMAIL,
        to: user.email,
        subject,
        plainver: `A payment of $${payment.amount} was made for Award ID ${director.awardId} on ${paidDate}. Please ensure all relevant documents are updated.`,
        htmlver: parseHTMLTemplate(`${__dirname}/../email_templates/payment-reminder.html`, {
          username: user.name,
          amount: payment.amount.toFixed(2),
          awardId: director.awardId,
          paidDate,
          awardLink,
          clientName, // Add client name here
        }),
      });

      console.log(`Email sent to ${user.email}`);
    });

    await Promise.all(notifyProjectDirectors);
    return true;
  } catch (error) {
    console.error('Error sending payment reminders:', error);
    throw new Error('Error sending project director payment reminders');
  }
};

const sendNewGrantOpportunitiesNotification = async () => {
  try {
    const today = dayjs().format('YYYY-MM-DD');

    const newPrograms = await Program.findAll({
      where: {
        startsAt: today,
      },
      attributes: ['id', 'name', 'category', 'states'],
      order: [['createdAt', 'DESC']],
      limit: 3,
    });

    if (!newPrograms.length) {
      scheduleLogger.info('No new grant opportunities found for today');
      return true;
    }

    const programsWithFiles = await Promise.all(
      newPrograms.map(async (program: any) => {
        const files = await ProgramFiles.findAll({
          where: {
            program_id: program.id,
            enabled: true,
          },
          attributes: ['name', 'fileSummary'],
          limit: 1,
        });

        return {
          program: program.get({ plain: true }),
          file: files[0]?.get({ plain: true }) || null,
        };
      })
    );

    const validPrograms = programsWithFiles.filter(({ file }) => file !== null);

    if (!validPrograms.length) {
      scheduleLogger.info('No programs with valid files found');
      return true;
    }

    const programDetails = validPrograms.map(({ program, file }) => ({
      programName: program.name,
      fileName: file.name,
      grantSummary: file.fileSummary,
      category: program.category?.join(', ') || 'N/A',
      states: program.states?.join(', ') || 'N/A',
      programLink: `${process.env.PUBLIC_URL}/dashboard/program-details/${program.id}`,
    }));

    const employees = await Employee.findAll({
      where: {
        enabled: true,
        userType: {
          [Op.in]: [
            strings.users.userTypes.millenniumAdmin,
            strings.users.userTypes.millenniumManager,
            strings.users.userTypes.millenniumResearcher,
            strings.users.userTypes.millenniumAnalyst,
            strings.users.userTypes.userAdmin,
            strings.users.userTypes.userAnalyst,
          ],
        },
      },
      attributes: ['id', 'name', 'email'],
    });

    const sendEmails = employees.map(async (employee: any) => {
      try {
        const htmlContent = parseHTMLTemplate(
          `${__dirname}/../email_templates/new-grant-opportunity.html`,
          {
            programs: programDetails,
            viewMoreLink: `${process.env.PUBLIC_URL}/`,
          }
        );

        await sendEmailLetter({
          from: process.env.SERVICE_MAILER_EMAIL,
          to: employee.email,
          subject: 'New Grant Opportunities Available',
          plainver:
            'New grant opportunities have been posted. Please check your GrantTrack portal for details.',
          htmlver: htmlContent,
        });

        scheduleLogger.info(`Sent new grant opportunities email to ${employee.email}`);
      } catch (error) {
        scheduleLogger.error(`Error sending email to ${employee.email}: ${error}`);
      }
    });

    await Promise.all(sendEmails);
    return true;
  } catch (error) {
    console.error('Error sending new grant opportunities notifications:', error);
    throw new ApiError(
      httpStatus.BAD_REQUEST,
      'Error sending new grant opportunities notifications'
    );
  }
};

export default {
  sendComplianceReminder,
  sendUpcomingReportReminders,
  getNextDueReports,
  markPaymentsReminders,
  sendEmailDigest,
  sendPaymentReminderToProjectDirectors,
  sendNewGrantOpportunitiesNotification,
};
