/* eslint-disable no-nested-ternary */
/* eslint-disable no-use-before-define */
/* eslint-disable no-param-reassign */
import dayjs from 'dayjs';
import { Op } from 'sequelize';
import httpStatus from 'http-status';
import { omit, startCase } from 'lodash';

import strings from '../../config/strings';

import { updateAwardStatus } from './award-approval.service';

import ApiError from '../../helpers/api-error';

import db from '../../models';
import { AwardReportOuput } from '../../models/types/award-report';
import { AwardFileOuput } from '../../models/types/award-file';
import { AwardStatus, PresentationAwardStatuses } from '../../models/types/award';
import { createAwardLogSystemEntry } from '../action-log.service';

require('dotenv').config();

const { Award, AwardBudgetEntry, AwardFile, AwardReport, AwardPayment } = db;

interface TimelineItem {
  timeline: string;
  order: number;
  complete: boolean;
  status: string;
  requiredAction?: string | null;
  defaultAction?: string;
  children?: TimelineItem[];
}

interface calculateStatusDetailsInput {
  status: PresentationAwardStatuses;
  awardId: number | string;
  award: typeof Award;
  awardReports: AwardReportOuput[];
  awardBudget: typeof AwardBudgetEntry;
  contractDocumentExists: boolean;
  order: number;
}

const requiredActions: { [K in PresentationAwardStatuses]?: string | null } = {
  awarded: 'Upload application',

  applicationRequired: 'Upload award letter',
  awardLetterRequired: 'Upload resolution',
  resolutionRequired: 'Upload agreement',
  initialAgreementRequired: 'Add budget line items',

  appropriated: 'Add budget line items',

  budgetRequired: 'Input report schedule',
  reportScheduleRequired: 'Add required contract materials',
  contractMaterialRequired: 'Submit for approval',

  encumbered: null,
  approval: 'Approve award',

  active: null,

  complianceRequired: 'Mark as in-compliance',
  reportsRequired: 'Upload report document',

  closeout: 'Finish reports & payments',
  closed: 'The award is closed',
};

const { statusStrings, statusTimelineStrings } = strings.awards;

const hasDocument = async (
  category: (typeof strings.awards.fileCategories)[number],
  awardId?: number | string
) => {
  if (!awardId) return null;

  const files = await AwardFile.findAll({
    where: {
      awardId,
      category,
      enabled: true,
    },
  });

  return Boolean(files.length > 0);
};

// This function calculates the document status based on the completion of various required documents for an award
const calculateDocumentStatus = async (award: typeof Award) => {
  const initialApplicationDocumentExists = await hasDocument('initialApplication', award.id);
  const initialAwardLetterDocumentExists = await hasDocument('initialAwardLetter', award.id);
  const initialAgreementDocumentExists = await hasDocument('initialAgreement', award.id);
  const initialResolutionDocumentExists = await hasDocument('initialResolution', award.id);

  // Check if the initial application document is required and completed
  const initialApplicationRequiredCompleted =
    !award.applicationRequired || initialApplicationDocumentExists;

  // Check if the initial award letter document is required and completed
  const initialAwardLetterRequiredCompleted =
    initialApplicationRequiredCompleted &&
    (!award.awardLetterRequired || initialAwardLetterDocumentExists);

  // Check if the initial resolution document is required and completed
  const initialResolutionRequiredCompleted =
    initialApplicationRequiredCompleted &&
    initialAwardLetterRequiredCompleted &&
    (!award.resolutionRequired || initialResolutionDocumentExists);

  // Check if the initial agreement document is required and completed
  const initialAgreementRequiredCompleted =
    initialApplicationRequiredCompleted &&
    initialAwardLetterRequiredCompleted &&
    initialResolutionRequiredCompleted &&
    (!award.initialAgreementRequired || initialAgreementDocumentExists);

  // Determine the document status based on the completion of required documents
  if (initialAgreementRequiredCompleted) return 'budgetRequired';
  if (initialResolutionRequiredCompleted) return 'initialAgreementRequired';
  if (initialAwardLetterRequiredCompleted) return 'resolutionRequired';
  if (initialApplicationRequiredCompleted) return 'awardLetterRequired';

  return 'applicationRequired';
};

const calculateAppropriationStatus = async (award: typeof Award) => {
  const documentStatus = await calculateDocumentStatus(award);
  const documentsAreCompleted = documentStatus === 'budgetRequired';

  if (!documentsAreCompleted) return documentStatus;

  const hasBudget = await AwardBudgetEntry.findOne({
    where: {
      awardId: award.id,
      awardAmount: {
        [Op.gt]: 0,
      },
    },
  });

  const awardReport = await AwardReport.findOne({
    where: { awardId: award.id },
  });
  const hasAtLeastOneReport = Boolean(awardReport);

  const contractMaterial = await hasDocument('requiredContractMaterial', award.id);
  const hasContactMaterial = !award.contractMaterialRequired || contractMaterial;

  if (hasContactMaterial && documentsAreCompleted && hasBudget && hasAtLeastOneReport)
    return 'encumbered';
  if (documentsAreCompleted && hasBudget && hasAtLeastOneReport) return 'contractMaterialRequired';
  if (documentsAreCompleted && hasBudget) return 'reportScheduleRequired';

  return documentStatus;
};

const calculateStatusDetails = async ({
  status,
  awardId,
  award,
  awardReports,
  awardBudget,
  contractDocumentExists,
  order,
}: calculateStatusDetailsInput) => {
  const result: TimelineItem = {
    timeline: `${startCase(status)}`,
    order,
    status,
    complete: false,
    children: [],
  };

  // Set the required action for each status to the required action on the next status rather than the current status
  // result.requiredAction = requiredActions[status];
  const awardStatusIndex = Object.keys(statusStrings).indexOf(award.status);
  const currentStatusIndex = Object.keys(statusTimelineStrings).indexOf(status);
  // eslint-disable-next-line no-nested-ternary
  const statusOffset =
    currentStatusIndex <= 2 ? 1 : currentStatusIndex >= 6 ? 2 : currentStatusIndex >= 7 ? 3 : 0;
  const nextStatus = Object.keys(statusStrings)[currentStatusIndex - statusOffset];
  result.requiredAction = requiredActions[nextStatus as PresentationAwardStatuses] || undefined;

  const applicationDocumentExists = await hasDocument('initialApplication', awardId);

  if (status === 'awarded') {
    result.complete = true;
    // !award.applicationRequired ||
    // (award.applicationRequired && Boolean(applicationDocumentExists));
    result.timeline = 'Awarded';
    result.defaultAction = 'Upload application';
  } else if (status === 'applicationRequired') {
    result.complete =
      !award.applicationRequired ||
      (award.applicationRequired && Boolean(applicationDocumentExists));
    result.timeline = 'Application';
    result.defaultAction = 'Upload application';
  } else if (status === 'awardLetterRequired') {
    const documentExists = await hasDocument('initialAwardLetter', awardId);
    result.complete =
      !award.awardLetterRequired || (award.awardLetterRequired && Boolean(documentExists));
    result.timeline = 'Award Letter';
  } else if (status === 'initialAgreementRequired') {
    const documentExists = await hasDocument('initialAgreement', awardId);
    result.complete =
      !award.initialAgreementRequired ||
      (award.initialAgreementRequired && Boolean(documentExists));
    result.timeline = 'Agreement';
  } else if (status === 'resolutionRequired') {
    const documentExists = await hasDocument('initialResolution', awardId);
    result.complete =
      !award.resolutionRequired || (award.resolutionRequired && Boolean(documentExists));
    result.timeline = 'Resolution';
  } else if (status === 'appropriated') {
    result.complete =
      currentStatusIndex >= Object.keys(statusStrings).indexOf(statusStrings.resolutionRequired);
    result.requiredAction = 'Add budget line items';
    result.timeline = 'Appropriated';
  } else if (status === 'reportScheduleRequired') {
    result.complete = Boolean(awardReports.length);
    result.timeline = 'Report Schedule';
  } else if (status === 'budgetRequired') {
    result.complete = Boolean(awardBudget);
    result.timeline = 'Budget Input';
  } else if (status === 'contractMaterialRequired') {
    result.complete = !award.contractMaterialRequired || Boolean(contractDocumentExists);
    result.timeline = 'Contract Documents';
  } else if (status === 'encumbered') {
    result.complete = Boolean(awardReports.length && awardBudget && contractDocumentExists);
    result.timeline = 'Encumbered';
  } else if (status === 'approval') {
    result.complete = award.isApproved;
    result.timeline = 'Approval';
  } else if (status === 'active') {
    result.complete = true;

    if (awardReports.length > 0) {
      const getFile = async (schedule: AwardReportOuput, index: number) =>
        new Promise((callback) => {
          AwardFile.findOne({
            where: { reportId: schedule.id },
          }).then((scheduleReportFile: AwardFileOuput) => {
            const complete = Boolean(scheduleReportFile);
            const requiredAction = complete ? null : 'Upload report document';

            return callback({
              timeline: schedule.name || schedule.dueDate,
              reportId: schedule?.id,
              order: 10 + index,
              complete,
              requiredAction,
            });
          });
        });

      result.children = (await Promise.all(
        awardReports.map(async (schedule, index) => getFile(schedule, index))
      )) as never[];

      // Loop through the children to see if any have a due date of today or earlier and set the parent to complete
      const uncompletedReports = result.children.some(
        (child) =>
          !child.complete &&
          (dayjs(child.timeline).isBefore(dayjs()) || dayjs(child.timeline).isSame(dayjs()))
      );

      if (uncompletedReports) result.requiredAction = 'Upload report document';
    }
  } else if (status === 'complianceRequired') {
    result.complete = award.isCompliance;
    if (result.complete) result.requiredAction = null;
    result.timeline = 'Compliance Required';
  } else if (status === 'closeout') {
    result.complete = award.status === 'closed' || award.endsOn <= new Date(); // awardStatusIndex >= currentStatusIndex; // && (await calculateClosedStatus(award, awardId));
    result.timeline = 'Closeout';
    result.requiredAction = 'Finish reports & payments';
  } else if (status === 'closed') {
    result.complete = award.status === 'closed'; // awardStatusIndex >= currentStatusIndex;
    result.timeline = 'Closed';
    result.requiredAction = 'The award is closed';
  }

  // console.log(
  //   '---- result',
  //   result.timeline,
  //   currentStatusIndex,
  //   Object.keys(statusStrings).indexOf(statusStrings.resolutionRequired)
  // );

  return result;
};

const getComplianceDate = async (award: typeof Award) => {
  // Calculate the compliance date based by getting the date between the report date schedule of the all the reports on the award.
  // So if there are 8 reports, then the compliance date comes in after the 4th report.
  // Get all the reports on the award
  // const awardReports = await AwardReport.findAll({ awardId: award.id });
  // Get the due date of the reports
  // const reportDates = awardReports.map((report: typeof AwardReport) => report.dueDate);
  // Sort the dates in ascending order
  // reportDates.sort();
  // Get the middle date
  // const complianceDate = reportDates[Math.floor(reportDates.length / 2)];

  const [startDate, endDate] = [
    award.startsOn && dayjs(award.startsOn),
    award.endsOn && dayjs(award.endsOn),
  ];

  // Get the date inbetween the start and end date of the award
  const complianceDate = dayjs((startDate.valueOf() + endDate.valueOf()) / 2);

  return complianceDate;
};

export const calculateClosedStatus = async (award: typeof Award, awardId: string | number) => {
  const awardReportDates = await AwardReport.findAll({
    where: { awardId, enabled: true },
  });

  const awardReportFiles = await AwardFile.findAll({
    where: {
      awardId,
      reportId: { [Op.in]: awardReportDates.map((r: typeof AwardReport) => r.id) },
      enabled: true,
    },
  });

  const paymentsMade = await AwardPayment.findAll({
    where: {
      awardId,
      status: { [Op.ne]: null },
      enabled: true,
    },
  });

  const paymentsReceived = await AwardPayment.findAll({
    where: {
      awardId,
      enabled: true,
    },
  });

  const allPaymentsMade = paymentsMade.length === paymentsReceived.length;

  const result =
    paymentsMade.length > 0 &&
    awardReportDates.length > 0 &&
    award.paymentsReceived === award.paymentsRequested &&
    allPaymentsMade &&
    awardReportFiles.length === awardReportDates.length;

  // Uncomment to see the status for debugging
  // -
  console.log(
    'calculateClosedStatus',
    result,
    awardReportDates.length,
    awardReportFiles.length,
    award.paymentsReceived,
    award.paymentsRequested,
    paymentsMade.length,
    paymentsReceived.length,
    allPaymentsMade
  );

  return result;
};

const recalculateCompliance = async (award: typeof Award, timelines: TimelineItem[]) => {
  if (!award) return timelines;

  const [startDate, endDate] = [
    award.startsOn && dayjs(award.startsOn),
    award.endsOn && dayjs(award.endsOn),
  ];

  // Get the date inbetween the start and end date of the award
  const complianceDate = dayjs((startDate.valueOf() + endDate.valueOf()) / 2);

  // Move 'Compliance Required' to inbetween the reports schedule.
  // So reports '2024-01-01' and '2024-01-02' will mean compliance is required between those dates,
  // and not after the last report, so compliance required is '2024-01-01'
  const complianceIndex = timelines.findIndex(
    (timeline) => timeline.status === 'complianceRequired'
  );

  const reportSchedulefirstIndex = 11 + 2; // + awarded & appropriated
  const reportScheduleLastIndex = timelines.findIndex(
    (timeline, index) =>
      timeline.timeline.match(/\d{4}-\d{2}-\d{2}/) === null && index > reportSchedulefirstIndex
  );
  const newComplianceIndex =
    reportSchedulefirstIndex + Math.floor((reportScheduleLastIndex - reportSchedulefirstIndex) / 2);

  const complianceObject = timelines[complianceIndex];
  timelines.splice(complianceIndex, 1);
  // insert complianceObject in the middle of the report schedules
  timelines.splice(newComplianceIndex, 0, complianceObject);

  if (complianceDate && award.complianceDate !== complianceDate) {
    await award.update({ complianceDate });
    createAwardLogSystemEntry(award, -1); // No user to associate
  }

  // Re-order all timeline records
  timelines = timelines.map((timeline, index) => ({
    ...timeline,
    order: index,
  }));

  return timelines;
};

export const getAwardTimelines = async (awardId: string | number, privatelyCalled = false) => {
  try {
    const award = awardId ? await Award.findByPk(awardId) : null;
    const allStatuses = Object.keys(statusTimelineStrings).filter(
      (status) => status !== 'reportsRequired'
    );

    if (!award) return [];

    if (!privatelyCalled) syncAwardStatus(awardId, true); // Recalculate compliance

    const awardReports: AwardReportOuput[] = await AwardReport.findAll({
      where: { awardId },
      order: [['dueDate', 'ASC']],
    });

    const awardBudget = await AwardBudgetEntry.findOne({
      where: {
        awardId,
        [Op.or]: [
          {
            awardAmount: {
              [Op.gt]: 0,
            },
          },
          {
            awardAmount: {
              [Op.lt]: 0,
            },
          },
          {
            awardBalance: {
              [Op.gt]: 0,
            },
          },
          {
            awardBalance: {
              [Op.lt]: 0,
            },
          },
        ],
      },
    });

    const contractDocumentExists =
      !award.contractMaterialRequired ||
      Boolean(await hasDocument('requiredContractMaterial', awardId));

    let timelines: TimelineItem[] = [];
    const operations = allStatuses.map((item, index) => {
      const status = allStatuses[index] as string as PresentationAwardStatuses;

      return calculateStatusDetails({
        awardId,
        award,
        status,
        awardReports,
        awardBudget,
        contractDocumentExists,
        order: index,
      });
    });
    timelines = await Promise.all(operations);

    timelines = (timelines as TimelineItem[])?.reduce(
      (acc: TimelineItem[], timeline) => [
        ...acc,
        omit(timeline, 'children'),
        ...(timeline.children || []),
      ],
      []
    );

    if (awardReports.length > 1) timelines = await recalculateCompliance(award, timelines);

    // Mark all previous timelines as not complete up to the first completed timeline that is completed ahead of other steps
    const firstUnCompletedIndex = timelines.findIndex((timeline) => !timeline.complete);

    // Ignore everything after active
    if (!['active', 'complianceRequired', 'closeout', 'closed'].includes(award.status))
      timelines.forEach((timeline, index) => {
        if (index >= firstUnCompletedIndex) {
          // Ignore report timeline dates
          if (timeline.timeline.match(/\d{4}-\d{2}-\d{2}/) !== null) return;

          timelines[index].complete = false;
        }
      });

    return timelines;
  } catch (error) {
    console.error(error);
    throw new ApiError(httpStatus.BAD_REQUEST, 'Error retieving award timelines');
  }
};

export async function syncAwardStatus(awardId?: string | number | null, privatelyCalled = false) {
  if (!awardId) {
    console.error('Award ID is required to sync the award status.');
    return false;
  }

  if (!privatelyCalled) getAwardTimelines(awardId, true); // Recalculate compliance

  const award = await Award.findByPk(awardId);

  if (award.status === 'approval') return true;

  let newStatus: AwardStatus | null = null;

  switch (award.status) {
    case 'awarded':
      newStatus = (await calculateDocumentStatus(award)) as string as AwardStatus;
      break;
    case 'applicationRequired':
      newStatus = (await calculateDocumentStatus(award)) as string as AwardStatus;
      break;
    case 'awardLetterRequired':
      newStatus = (await calculateDocumentStatus(award)) as string as AwardStatus;
      break;
    case 'initialAgreementRequired':
      newStatus = (await calculateDocumentStatus(award)) as string as AwardStatus;
      break;
    case 'resolutionRequired':
      newStatus = (await calculateDocumentStatus(award)) as string as AwardStatus;
      break;
    case 'budgetRequired':
      newStatus = (await calculateAppropriationStatus(award)) as string as AwardStatus;
      break;
    case 'reportScheduleRequired':
      newStatus = (await calculateAppropriationStatus(award)) as string as AwardStatus;
      break;
    case 'contractMaterialRequired':
      newStatus = (await calculateAppropriationStatus(award)) as string as AwardStatus;
      break;
    // Approval is skipped - required frontend action
    // case 'appropriated':
    //   // if (appropriated) newStatus = 'encumbered';
    //   break;
    // case 'encumbered':
    //   // if (appropriated) newStatus = 'approval';
    //   break;
    // case 'approval':
    //   // if (award.isApproved) newStatus = 'active';
    //   // if (award.isRejected) newStatus = 'encumbered';
    //   // else newStatus = 'approval';
    //   break;
    case 'active':
      if (!award.isCompliance) {
        const complianceDate = await getComplianceDate(award);

        if (dayjs().isAfter(dayjs(complianceDate)) || dayjs().isSame(dayjs(complianceDate)))
          newStatus = 'complianceRequired';
      }

      if (
        award.isApproved &&
        award.isCompliance &&
        (dayjs(award.endsOn).isBefore(dayjs()) || dayjs(award.endsOn).isSame(dayjs())) &&
        !['closeout', 'closed'].includes(award.status)
      )
        newStatus = 'closeout';
      break;
    // case 'complianceRequired':
    //   if (award.isCompliance) newStatus = 'reportsRequired';
    //   break;
    // case 'reportsRequired':
    //   // if (hasAtLeastOneReport) newStatus = 'complianceRequired';
    //   break;
    case 'closeout': {
      // const isClosed = await calculateClosedStatus(award, awardId);
      if (dayjs(award.endsOn).isAfter(dayjs())) newStatus = 'active';
      // else if (isClosed) newStatus = 'closed';
      break;
    }
    case 'closed':
      if (dayjs(award.endsOn).isAfter(dayjs())) newStatus = 'active';
      break;
    default:
      break;
  }

  if (
    ![
      'awarded',
      'applicationRequired',
      'awardLetterRequired',
      'initialAgreementRequired',
      'resolutionRequired',
      'budgetRequired',
      'reportScheduleRequired',
      'contractMaterialRequired',
    ].includes(award.status)
  ) {
    const phase1Status = (await calculateDocumentStatus(award)) as string as AwardStatus;

    if (phase1Status !== 'budgetRequired') newStatus = phase1Status;
    else {
      const phase2Status = (await calculateAppropriationStatus(award)) as string as AwardStatus;

      if (phase2Status !== 'encumbered') newStatus = phase2Status;
    }
  }

  // Uncomment to debug the status
  // console.log('Status Debug', award.status, newStatus);
  // BASH on server: tail -f /home/<USER>/.pm2/logs/*.log | grep --line-buffered 'Status Debug'

  if (award?.status !== newStatus && newStatus) {
    await updateAwardStatus(awardId as number, newStatus);
    createAwardLogSystemEntry({ status: award.status, id: award.id }, -1); // No user to associate
  }

  return true;
}

export default {
  syncAwardStatus,
  getAwardTimelines,
};
