// Test cron schedule timing
// Usage: node src/helpers/scripts/js/test-cron-schedule.js

const cron = require('node-cron');

// Test schedules with shorter intervals for debugging
const testSchedules = [
  {
    name: 'Every Minute Test',
    schedule: '* * * * *',
    description: 'Runs every minute for testing'
  },
  {
    name: 'Every 5 Minutes Test',
    schedule: '*/5 * * * *',
    description: 'Runs every 5 minutes for testing'
  },
  {
    name: 'Production Daily Digest',
    schedule: '0 0 * * *',
    description: 'Daily at midnight (production schedule)'
  },
  {
    name: 'Production Weekly Digest',
    schedule: '0 0 * * 0',
    description: 'Sunday at midnight (production schedule)'
  }
];

function validateCronExpression(schedule) {
  try {
    return cron.validate(schedule);
  } catch (error) {
    return false;
  }
}

function getNextExecutionTimes(schedule, count = 5) {
  if (!validateCronExpression(schedule)) {
    return ['Invalid cron expression'];
  }
  
  const times = [];
  const now = new Date();
  
  // This is a simplified calculation - for production use a proper cron library
  // For demonstration purposes only
  times.push('Use a proper cron calculator for exact times');
  
  return times;
}

function testSchedules() {
  console.log('🕐 CRON SCHEDULE TESTER');
  console.log('=======================\n');
  
  testSchedules.forEach(({ name, schedule, description }) => {
    const isValid = validateCronExpression(schedule);
    const status = isValid ? '✅ Valid' : '❌ Invalid';
    
    console.log(`${name}:`);
    console.log(`  Schedule: ${schedule}`);
    console.log(`  Description: ${description}`);
    console.log(`  Status: ${status}`);
    
    if (isValid) {
      console.log(`  Next executions: Use 'crontab.guru' or similar tool to check`);
    }
    console.log('');
  });
}

function startTestCron() {
  console.log('🚀 Starting test cron job (runs every minute)...');
  console.log('Press Ctrl+C to stop\n');
  
  let counter = 0;
  
  const task = cron.schedule('* * * * *', () => {
    counter++;
    const now = new Date().toISOString();
    console.log(`[${now}] Test cron execution #${counter}`);
    
    if (counter >= 5) {
      console.log('\n✅ Test completed after 5 executions');
      task.stop();
      process.exit(0);
    }
  });
  
  task.start();
}

// Parse command line arguments
const args = process.argv.slice(2);
const command = args[0];

switch (command) {
  case 'test':
    startTestCron();
    break;
  case 'validate':
  default:
    testSchedules();
    break;
}
