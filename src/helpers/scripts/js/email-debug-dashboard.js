// Email Debug Dashboard
// Usage: npm run build && node dist/helpers/scripts/js/email-debug-dashboard.js
// Or: node src/helpers/scripts/js/email-debug-dashboard.js (if using compiled JS)

require('dotenv').config();

// Try to load from dist first (compiled), then from src
let db;
let scheduleLogger;
let scheduleService;

try {
  // Try compiled version first
  db = require('../../../dist/models').default;
  scheduleLogger = require('../../../dist/config/logger').scheduleLogger;
} catch (error) {
  try {
    // Fallback to source (this won't work with TypeScript imports)
    console.log('Compiled version not found, you need to run: npm run build');
    process.exit(1);
  } catch (srcError) {
    console.error('Could not load modules. Please run: npm run build');
    process.exit(1);
  }
}

const { NotificationPreference, AwardNotification, Employee, Program } = db;

async function checkEmailConfiguration() {
  console.log('\n🔧 EMAIL CONFIGURATION CHECK');
  console.log('================================');

  const requiredEnvVars = [
    'SENDGRID_API_KEY',
    'SERVICE_MAILER_EMAIL',
    'PUBLIC_URL',
    'FRONTEND_PORT',
  ];

  requiredEnvVars.forEach((varName) => {
    const value = process.env[varName];
    console.log(`${varName}: ${value ? '✅ Set' : '❌ Missing'}`);
    if (value && varName === 'SERVICE_MAILER_EMAIL') {
      console.log(`  └─ Value: ${value}`);
    }
  });
}

async function checkNotificationPreferences() {
  console.log('\n📧 NOTIFICATION PREFERENCES');
  console.log('============================');

  try {
    const frequencies = ['daily', 'weekly', 'biWeekly', 'monthly', 'yearly'];

    for (const frequency of frequencies) {
      const preferences = await NotificationPreference.findAll({
        where: { frequency, preference: 'awardBatchEmails' },
        include: ['user'],
      });

      console.log(`${frequency.toUpperCase()}: ${preferences.length} users subscribed`);

      if (preferences.length > 0) {
        preferences.forEach((pref, index) => {
          const { user } = pref;
          console.log(`  ${index + 1}. ${user?.name || 'Unknown'} (${user?.email || 'No email'})`);
        });
      }
    }
  } catch (error) {
    console.error('❌ Error checking notification preferences:', error.message);
  }
}

async function checkRecentNotifications() {
  console.log('\n📬 RECENT NOTIFICATIONS (Last 7 days)');
  console.log('======================================');

  try {
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);

    const recentNotifications = await AwardNotification.findAll({
      where: {
        createdAt: {
          [db.Sequelize.Op.gte]: sevenDaysAgo,
        },
      },
      order: [['createdAt', 'DESC']],
      limit: 10,
      attributes: ['type', 'subject', 'createdAt', 'enabled'],
    });

    if (recentNotifications.length === 0) {
      console.log('No notifications found in the last 7 days');
    } else {
      recentNotifications.forEach((notification, index) => {
        const date = new Date(notification.createdAt).toLocaleDateString();
        const status = notification.enabled ? '✅' : '❌';
        console.log(
          `  ${index + 1}. [${date}] ${status} ${notification.type}: ${notification.subject}`
        );
      });
    }
  } catch (error) {
    console.error('❌ Error checking recent notifications:', error.message);
  }
}

async function checkUpcomingReports() {
  console.log('\n📊 UPCOMING REPORTS (Next 30 days)');
  console.log('===================================');

  try {
    const scheduleService = require('../../../dist/services/schedule.service').default;
    const upcomingReports = await scheduleService.getNextDueReports();

    console.log(`Found ${upcomingReports.length} reports due in the next 30 days`);

    if (upcomingReports.length > 0) {
      upcomingReports.slice(0, 5).forEach((report, index) => {
        const dueDate = new Date(report.dueDate).toLocaleDateString();
        console.log(
          `  ${index + 1}. Award ${report.awardId} - Due: ${dueDate} - Assignee: ${
            report.assigneeId
          }`
        );
      });

      if (upcomingReports.length > 5) {
        console.log(`  ... and ${upcomingReports.length - 5} more`);
      }
    }
  } catch (error) {
    console.error('❌ Error checking upcoming reports:', error.message);
  }
}

async function checkNewGrantPrograms() {
  console.log('\n🎯 NEW GRANT PROGRAMS (Today)');
  console.log('==============================');

  try {
    const today = new Date().toISOString().split('T')[0];

    const newPrograms = await Program.findAll({
      where: {
        startsAt: today,
      },
      attributes: ['id', 'name', 'category', 'states'],
      order: [['createdAt', 'DESC']],
      limit: 10,
    });

    console.log(`Found ${newPrograms.length} programs starting today`);

    if (newPrograms.length > 0) {
      newPrograms.forEach((program, index) => {
        console.log(`  ${index + 1}. ${program.name} (${program.category})`);
      });
    }
  } catch (error) {
    console.error('❌ Error checking new grant programs:', error.message);
  }
}

async function checkCronSchedules() {
  console.log('\n⏰ CRON SCHEDULES');
  console.log('=================');

  const schedules = [
    { name: 'Automated Notifications', cron: '0 9 * * *', description: 'Daily at 9:00 AM' },
    { name: 'Payment Reminders', cron: '0 9 * * *', description: 'Daily at 9:00 AM' },
    { name: 'Daily Digest', cron: '0 0 * * *', description: 'Daily at midnight' },
    { name: 'Weekly Digest', cron: '0 0 * * 0', description: 'Sunday at midnight' },
    { name: 'Bi-Weekly Digest', cron: '0 0 * * 4', description: 'Thursday at midnight' },
    { name: 'Monthly Digest', cron: '0 0 1 * *', description: '1st of month at midnight' },
    { name: 'Yearly Digest', cron: '0 0 1 1 *', description: 'January 1st at midnight' },
  ];

  schedules.forEach((schedule) => {
    console.log(`  ${schedule.name}: ${schedule.cron} (${schedule.description})`);
  });
}

async function runFullDiagnostic() {
  console.log('🚀 EMAIL SYSTEM DIAGNOSTIC DASHBOARD');
  console.log('=====================================');
  console.log(`Timestamp: ${new Date().toISOString()}\n`);

  await checkEmailConfiguration();
  await checkCronSchedules();
  await checkNotificationPreferences();
  await checkRecentNotifications();
  await checkUpcomingReports();
  await checkNewGrantPrograms();

  console.log('\n✅ Diagnostic complete!');
  console.log('\nNext steps:');
  console.log('1. Run manual tests: node src/helpers/scripts/js/test-batch-emails.js');
  console.log('2. Check logs: tail -f src/log/schedule.log');
  console.log('3. Test individual functions with the test script');

  process.exit(0);
}

runFullDiagnostic().catch((error) => {
  console.error('❌ Diagnostic failed:', error);
  process.exit(1);
});
