// Test script for batch email functionality
// Usage: npx ts-node src/helpers/scripts/ts/test-batch-emails.ts [function_name]

import dotenv from 'dotenv';
import scheduleService from '../../../services/schedule.service';
import { scheduleLogger } from '../../../config/logger';

dotenv.config();

async function testEmailDigest(frequency = 'daily') {
  console.log(`\n=== Testing Email Digest (${frequency}) ===`);
  try {
    await scheduleService.sendEmailDigest(frequency);
    console.log(`✅ Email digest (${frequency}) completed successfully`);
  } catch (error: any) {
    console.error(`❌ Email digest (${frequency}) failed:`, error.message);
  }
}

async function testUpcomingReportReminders() {
  console.log('\n=== Testing Upcoming Report Reminders ===');
  try {
    await scheduleService.sendUpcomingReportReminders();
    console.log('✅ Upcoming report reminders completed successfully');
  } catch (error: any) {
    console.error('❌ Upcoming report reminders failed:', error.message);
  }
}

async function testPaymentReminders() {
  console.log('\n=== Testing Payment Reminders ===');
  try {
    await scheduleService.sendPaymentReminderToProjectDirectors();
    console.log('✅ Payment reminders completed successfully');
  } catch (error: any) {
    console.error('❌ Payment reminders failed:', error.message);
  }
}

async function testNewGrantOpportunities() {
  console.log('\n=== Testing New Grant Opportunities ===');
  try {
    await scheduleService.sendNewGrantOpportunitiesNotification();
    console.log('✅ New grant opportunities notification completed successfully');
  } catch (error: any) {
    console.error('❌ New grant opportunities notification failed:', error.message);
  }
}

async function testGetNextDueReports() {
  console.log('\n=== Testing Get Next Due Reports ===');
  try {
    const reports = await scheduleService.getNextDueReports();
    console.log(`✅ Found ${reports.length} upcoming reports due`);
    if (reports.length > 0) {
      console.log('Sample report:', JSON.stringify(reports[0], null, 2));
    }
  } catch (error: any) {
    console.error('❌ Get next due reports failed:', error.message);
  }
}

async function runAllTests() {
  console.log('🚀 Starting batch email system tests...\n');

  await testNewGrantOpportunities();

  console.log('\n🏁 All tests completed!');
  process.exit(0);
}

// Parse command line arguments
const args = process.argv.slice(2);
const testFunction = args[0];

switch (testFunction) {
  case 'digest':
    testEmailDigest(args[1] || 'daily');
    break;
  case 'reports':
    testUpcomingReportReminders();
    break;
  case 'payments':
    testPaymentReminders();
    break;
  case 'grants':
    testNewGrantOpportunities();
    break;
  case 'data':
    testGetNextDueReports();
    break;
  case 'all':
  default:
    runAllTests();
    break;
}
