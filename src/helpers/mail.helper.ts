import fs from 'fs';
import handlebars from 'handlebars';

import sgMail from '@sendgrid/mail';

require('dotenv').config();

const { SENDGRID_API_KEY } = process.env;

type PlaceholderReplacements = Record<string, any>;

export const parseHTMLTemplate = (
  htmlPath: string | Buffer | fs.PathLike,
  placeholderReplacements: PlaceholderReplacements
) => {
  const htmlFile = fs.readFileSync(htmlPath, { encoding: 'utf-8' });
  const template = handlebars.compile(htmlFile);
  const htmlToSend = template(placeholderReplacements);

  return htmlToSend;
};

export type MailAttachment = {
  filename: string;
  content: string;
};
export type EmailData = {
  to: string | string[];
  bcc?: string[];
  from?: string;
  replyTo?: string;
  subject?: string;
  plainver?: string;
  htmlver?: string;
  attachments?: MailAttachment[];
};

function removeBCCDuplicates(a: EmailData) {
  const toEmails = Array.isArray(a.to) ? a.to : a.to.split(',');

  return {
    ...a,
    to: toEmails.filter((email, index) => toEmails.indexOf(email) === index),
    bcc: (a.bcc ?? []).filter((bccEmail) => !toEmails.includes(bccEmail)),
  };
}

export const sendEmailLetter = async (mailOptions: EmailData): Promise<any> => {
  const options = removeBCCDuplicates(mailOptions);

  // console.log('sendEmailLetter - Options', options);
  console.log('sendEmailLetter - From & To', options.from, options.to);

  sgMail.setApiKey(SENDGRID_API_KEY || '');

  return;

  const result = await sgMail
    .send({
      from: options.from || process.env.SERVICE_MAILER_EMAIL || '',
      replyTo: options.replyTo || process.env.SERVICE_MAILER_EMAIL,
      to: options.to,
      bcc: options.bcc,
      subject: options.subject,
      text: options.plainver || '',
      html: options.htmlver,

      attachments: options.attachments ?? [],
    })
    .catch((error) => {
      if (error) console.error('sendEmailLetter - Error', error, error?.response?.body?.errors);
    });

  console.log('sendEmailLetter - Result', result);

  return result;
};

module.exports = { parseHTMLTemplate, sendEmailLetter };
