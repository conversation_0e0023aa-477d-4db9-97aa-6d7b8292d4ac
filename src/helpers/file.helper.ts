import {
  PutObjectCommand,
  GetObjectCommand,
  DeleteObjectCommand,
  S3Client,
} from '@aws-sdk/client-s3';
import { Readable } from 'stream';
import { promisify } from 'util';
import libre from 'libreoffice-convert';
import path from 'path';

const convertAsync = promisify(libre.convert);
require('dotenv').config();

const { NODE_ENV } = process.env;

export const filePrefix = NODE_ENV === 'development' ? `${__dirname}/..` : '/app';

/**
 * Sanitizes a filename by removing any invalid characters and replacing them with hyphens.
 * Invalid characters are defined as any character that is not a letter, number, space, or one of the following: `()-.,_]]`.
 * A Unicode-aware regex is used to match invalid characters instead of the usual ASCII-only regex: /[^a-z0-9 \-().,_\]\]]/i
 * @param filename - The original filename to be sanitized.
 * @returns The sanitized filename.
 */
export const sanitizeFilename = (filename: string) =>
  // eslint-disable-next-line node/no-unsupported-features/es-syntax
  filename
    .trim()
    .replace(/[^a-z0-9 \-().,_\]\]]/gi, '-')
    .replace(/--+/gi, '-')
    .replace(/summaries\//gu, 'summaries/');

export const s3Upload = async (
  s3: S3Client,
  bucket: string,
  body: Blob | Buffer | string,
  key: string
) => {
  try {
    await s3.send(
      new PutObjectCommand({ Bucket: bucket, Key: key, Body: body, ACL: 'public-read' })
    );

    return { ok: true };
  } catch (error) {
    console.error('s3Upload', error);
    return { ok: false, error };
  }
};

export const s3Retrieve = async (
  s3: S3Client,
  bucket: string,
  key: string,
  writeStream: NodeJS.WritableStream
) => {
  try {
    const s3Item = await s3.send(new GetObjectCommand({ Key: key, Bucket: bucket }));

    if (s3Item.Body instanceof Readable) s3Item.Body.pipe(writeStream);

    return { ok: true, item: s3Item };
  } catch (error) {
    console.error('s3Retrieve', error);
    return { ok: false, error };
  }
};

export const s3Delete = async (s3: S3Client, bucket: string, key: string) => {
  try {
    await s3.send(new DeleteObjectCommand({ Bucket: bucket, Key: key }));
    return { ok: true };
  } catch (error) {
    console.error('s3Delete', error);
    return { ok: false, error };
  }
};

export const getFileStream = async (key: string, s3: S3Client, bucket = process.env.AWS_BUCKET) => {
  const command = new GetObjectCommand({
    Bucket: bucket,
    Key: key,
  });

  const response = await s3.send(command);
  return response.Body as NodeJS.ReadableStream;
};
export async function convertToPDF(buffer: Buffer): Promise<Buffer> {
  try {
    // Configure PDF export options to preserve hyperlinks
    // This filter ensures that all hyperlinks (including those without full protocols)
    // are preserved as clickable links in the PDF output
    const pdfExportFilter =
      'writer_pdf_Export:{"PDFViewSelection":{"type":"long","value":"0"},"ExportLinksRelativeFsys":{"type":"boolean","value":"false"},"ConvertOOoTargetToPDFTarget":{"type":"boolean","value":"true"},"ExportBookmarksToPDFDestination":{"type":"boolean","value":"true"}}';

    return await convertAsync(buffer, '.pdf', pdfExportFilter);
  } catch (error) {
    console.error('Error converting file to PDF:', error);
    throw error;
  }
}

export const generateFileSummary = async (
  fileBuffer: Buffer,
  fileName: string
): Promise<string> => {
  const fileType = path.extname(fileName).toLowerCase();

  if (['.txt', '.csv', '.json', '.js', '.ts', '.html', '.css'].includes(fileType)) {
    const content = fileBuffer.toString('utf-8');

    return content.replace(/\s+/g, ' ').substring(0, 1000).trim();
  }

  return ''; // No content available
};

module.exports = {
  s3Upload,
  s3Retrieve,
  s3Delete,
  filePrefix,
  sanitizeFilename,
  getFileStream,
  convertToPDF,
  generateFileSummary,
};
