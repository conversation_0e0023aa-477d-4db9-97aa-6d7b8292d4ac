{"name": "millenium-server", "version": "1.5.4", "description": "Millenium Strategies Admin Panel Server", "main": "src/server.ts", "engines": {"node": ">=12.0.0"}, "scripts": {"create-program-pdfs": "ts-node src/helpers/scripts/ts/create-program-pdfs.ts", "import-data": "node src/helpers/scripts/js/generate-staff-and-applications.js", "send-email-test": "node src/helpers/scripts/js/send-email-test.js", "test-batch-emails": "node src/helpers/scripts/js/test-batch-emails.js", "email-debug-dashboard": "node src/helpers/scripts/js/email-debug-dashboard.js", "update-filter-data": "node src/helpers/scripts/js/update-filters-to-camel-case.js", "camel-case-action-logs": "node src/helpers/scripts/js/camel-case-action-logs.js", "trim-funders": "node src/helpers/scripts/js/trim-funders.js", "build": "npx tsc; cp -r src/email_templates dist", "start": "npm run build && node dist/server.js", "start-server": "npm run build; NODE_OPTIONS='--max-http-header-size=245760' pm2 start dist/server.js --name millennium -i 4 --max-memory-restart 1G", "restart-server": "pm2 stop all; pm2 delete millennium; NODE_OPTIONS='--max-http-header-size=245760' pm2 start dist/server.js --name millennium -i 4 --max-memory-restart 1G", "dev": "nodemon src/server.ts", "db:migrate": "node ./node_modules/.bin/sequelize db:migrate --migrations-path ./src/db/migrations", "db:seed": "node ./node_modules/.bin/sequelize db:seed --seeders-path ./src/db/seeders", "db:gen": "node ./node_modules/.bin/sequelize migration:generate --migrations-path ./src/db/migrations --name", "db:sync": "ts-node src/helpers/scripts/js/sync-models.js", "prepare": "husky install", "test-debug": "node --inspect-brk node_modules/.bin/jest --detectOpenHandles --forceExit --verbose --coverage --runInBand ./src/tests", "test": "NODE_OPTIONS='--max-http-header-size=24576' node_modules/.bin/jest ./src/tests --detectOpenHandles --forceExit"}, "lint-staged": {"*.ts": ["prettier --write --ignore-unknown", "eslint --fix --ext .ts"]}, "author": "<PERSON><PERSON><PERSON>", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.33.0", "@aws-sdk/client-ses": "^3.33.0", "@sendgrid/mail": "^7.7.0", "@types/sequelize": "^4.28.19", "angular-expressions": "^1.1.4", "aws-sdk": "^2.991.0", "axios": "^1.10.0", "body-parser": "^1.19.0", "cors": "^2.8.5", "dayjs": "^1.10.7", "docxtemplater": "^3.25.2", "dotenv": "^10.0.0", "exceljs": "^4.3.0", "express": "^4.17.1", "express-fileupload": "^1.2.1", "express-unless": "^1.0.0", "fast-csv": "^4.3.6", "handlebars": "^4.7.7", "http-status": "^1.7.3", "jest": "^29.7.0", "joi": "^17.13.1", "jsonwebtoken": "^9.0.2", "libreoffice-convert": "^1.6.1", "lodash": "^4.17.21", "lodash.assign": "^4.2.0", "morgan": "^1.10.0", "multer": "^1.4.3", "nanoid": "^3.1.25", "node-cron": "^3.0.3", "nodemon": "^2.0.12", "npm": "^10.2.3", "pg": "^8.8.0", "pg-hstore": "^2.3.4", "pizzip": "^3.1.1", "request": "^2.88.2", "sequelize": "^6.35.1", "shelljs": "^0.8.4", "supertest": "^6.3.3", "winston": "^3.13.0"}, "devDependencies": {"@types/cors": "^2.8.16", "@types/exceljs": "^1.3.0", "@types/express": "^4.17.21", "@types/jest": "^29.5.10", "@types/morgan": "^1.9.9", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.14", "@types/pizzip": "^3.0.5", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "eslint": "^8.55.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-config-node": "^4.1.0", "eslint-config-prettier": "9.0.0", "eslint-import-resolver-typescript": "^3.6.1", "eslint-plugin-import": "^2.29.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.0.1", "husky": "^8.0.3", "lint-staged": "^15.0.2", "prettier": "3.0.3", "sequelize-cli": "^6.2.0", "ts-jest": "^29.1.1", "ts-node-dev": "^2.0.0", "typescript": "^5.2.2", "xmimetype": "^1.1.1"}}